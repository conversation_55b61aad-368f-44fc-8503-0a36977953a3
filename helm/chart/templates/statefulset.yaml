apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "data-pusher.fullname" . }}
  labels:
    {{- include "data-pusher.labels" . | nindent 4 }}
    app.kubernetes.io/component: gunicorn
spec:
  replicas: {{ .Values.dataPusher.replicaCount }}
  selector:
    matchLabels:
      {{- include "data-pusher.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: gunicorn
  serviceName: data-pusher
  template:
    metadata:
      labels:
        {{- include "data-pusher.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: gunicorn
    spec:
      {{- with .Values.dataPusher.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      affinity:
        {{- with .Values.dataPusher.nodeAffinity }}
        nodeAffinity:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
#      - name: alpine-editor
#        image: alpine:edge
#        command:
#          - sh
#          - -c
#          - 'apk add --no-cache vim && while true; do sleep 10; done'
#        volumeMounts:
#          - name: state
#            mountPath: /opt/coinmetrics/data-pusher
        {{- if .Values.celery.beat.create }}
        - command: [ "start-celery-beat" ]
          env:
            - name: CM_DATA_PUSHER_DB
              value: "{{ .Values.dataPusher.database }}"
            - name: DJANGO_SETTINGS_MODULE
              value: "{{ .Values.dataPusher.settingsModule }}"
            - name: CM_DATA_PUSHER_REDIS_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: redisUsername
                  optional: false
            - name: CM_DATA_PUSHER_REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: redisPassword
                  optional: false
            - name: CM_DATA_PUSHER_CELERY_BROKER_URL
              value: redis://$(CM_DATA_PUSHER_REDIS_USERNAME):$(CM_DATA_PUSHER_REDIS_PASSWORD)@{{ include "data-pusher.fullname" . }}-{{ .Values.redis.host }}:{{ .Values.redis.port }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          name: celery-beat
          {{- with .Values.celery.beat.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: state
              mountPath: /opt/coinmetrics/data-pusher
        {{- end }}
        {{- if .Values.celery.worker.create }}
        - command: [ "start-celery-worker" ]
          env:
            - name: OAUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: oauthClientId
                  optional: false
            - name: OAUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: oauthClientSecret
                  optional: false
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: pgPassword
                  optional: false
            - name: GOOGLE_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: googleClientID
                  optional: false
            - name: GOOGLE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: googleClientSecret
                  optional: false
            - name: GOOGLE_REFRESH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: googleRefreshToken
                  optional: false
            - name: OAUTH_BASE_REDIRECT_URI
              value: "{{ .Values.dataPusher.oauthBaseRedirectUri }}"
            - name: FIDELITY_INDEX_URL
              value: "{{ .Values.dataPusher.fidelityIndexUrl }}"
            - name: PGHOST
              value: "{{ .Values.dataPusher.pgHost }}"
            - name: PGUSER
              value: "{{ .Values.dataPusher.pgUser }}"
            - name: PG_SCHEMA
              value: "{{ .Values.dataPusher.environment }}"
            - name: PG_TRADES_DB
              value: "{{ .Values.dataPusher.pgDatabaseTrades }}"
            - name: PG_INDEX_DB
              value: "{{ .Values.dataPusher.pgDatabaseIndices }}"
            - name: ENVIRONMENT
              value: "{{ .Values.dataPusher.environment }}"
            - name: LOCATION
              value: "{{ .Values.dataPusher.location }}"
            - name: CM_DATA_PUSHER_DB
              value: "{{ .Values.dataPusher.database }}"
            - name: CM_DATA_PUSHER_REDIS_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: redisUsername
                  optional: false
            - name: CM_DATA_PUSHER_REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: redisPassword
                  optional: false
            - name: CM_DATA_PUSHER_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: dataPusherCookieSecretKey
                  optional: false
            - name: CM_DATA_PUSHER_SECRET_KEY_OVERRIDE
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: dataPusherCookieSecretKeyOverride
                  optional: false
            - name: CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY_OVERRIDE
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: dataPusherCookieFieldEncryptionKeyOverride
                  optional: false
            - name: CM_DATA_PUSHER_CELERY_BROKER_URL
              value: redis://$(CM_DATA_PUSHER_REDIS_USERNAME):$(CM_DATA_PUSHER_REDIS_PASSWORD)@{{ include "data-pusher.fullname" . }}-{{ .Values.redis.host }}:{{ .Values.redis.port }}
            - name: CM_SFTP_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: coinmetricsSFTPKey
                  optional: false
            - name: CM_SFTP_RSA_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: coinmetricsSftpRsaKey
                  optional: false
            - name: MINIO_URL
              value: "{{ .Values.dataPusher.minioUrl }}"
            - name: MINIO_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: minioAccessKeyID
                  optional: false
            - name: MINIO_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: minioSecretAccessKey
                  optional: false
            - name: SENDGRID_EMAIL
              value: "{{ .Values.dataPusher.sendgridEmail }}"
            - name: CM_SFTP_SERVER
              value: "{{ .Values.dataPusher.CM_SFTP_SERVER }}"
            - name: CM_SFTP_USERNAME
              value: "{{ .Values.dataPusher.CM_SFTP_USERNAME }}"
            - name: CM_SFTP_PORT
              value: "{{ .Values.dataPusher.CM_SFTP_PORT }}"
            - name: CM_SFTP_DATA_ROOT
              value: "{{ .Values.dataPusher.CM_SFTP_DATA_ROOT }}"
            - name: SENDGRID_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: sendgridApiKey
                  optional: false
            - name: CM_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: coinmetricsApiKey
                  optional: false
            - name: CM_API_BASE_URL
              value: "{{ .Values.dataPusher.coinmetricsApiBaseUrl }}"
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          name: celery-worker
          {{- with .Values.celery.worker.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: state
              mountPath: /opt/coinmetrics/data-pusher
        {{- end }}
        - command: [ "start-gunicorn" ]
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: OAUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: oauthClientId
                  optional: false
            - name: OAUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: oauthClientSecret
                  optional: false
            - name: CM_DATA_PUSHER_DEBUG
              value: "{{default .Values.dataPusher.debug false}}"
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: pgPassword
                  optional: false
            - name: GOOGLE_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: googleClientID
                  optional: false
            - name: GOOGLE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: googleClientSecret
                  optional: false
            - name: GOOGLE_REFRESH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: googleRefreshToken
                  optional: false
            - name: OAUTH_BASE_REDIRECT_URI
              value: "{{ .Values.dataPusher.oauthBaseRedirectUri }}"
            - name: FIDELITY_INDEX_URL
              value: "{{ .Values.dataPusher.fidelityIndexUrl }}"
            - name: PGHOST
              value: "{{ .Values.dataPusher.pgHost }}"
            - name: PGUSER
              value: "{{ .Values.dataPusher.pgUser }}"
            - name: PG_SCHEMA
              value: "{{ .Values.dataPusher.environment }}"
            - name: PG_TRADES_DB
              value: "{{ .Values.dataPusher.pgDatabaseTrades }}"
            - name: PG_INDEX_DB
              value: "{{ .Values.dataPusher.pgDatabaseIndices }}"
            - name: ENVIRONMENT
              value: "{{ .Values.dataPusher.environment }}"
            - name: LOCATION
              value: "{{ .Values.dataPusher.location }}"
            - name: CM_DATA_PUSHER_DB
              value: "{{ .Values.dataPusher.database }}"
            - name: DJANGO_SETTINGS_MODULE
              value: "{{ .Values.dataPusher.settingsModule }}"
            - name: CM_DATA_PUSHER_ALLOWED_HOSTS
              value: "{{ .Values.virtualServer.domain }},[$(POD_IP)]"
            - name: CM_DATA_PUSHER_CSRF_TRUSTED_ORIGINS
              value: "https://{{ .Values.virtualServer.domain }}"
            - name: SENDGRID_EMAIL
              value: "{{ .Values.dataPusher.sendgridEmail }}"
            - name: CM_SFTP_SERVER
              value: "{{ .Values.dataPusher.CM_SFTP_SERVER }}"
            - name: CM_SFTP_USERNAME
              value: "{{ .Values.dataPusher.CM_SFTP_USERNAME }}"
            - name: CM_SFTP_PORT
              value: "{{ .Values.dataPusher.CM_SFTP_PORT }}"
            - name: CM_SFTP_DATA_ROOT
              value: "{{ .Values.dataPusher.CM_SFTP_DATA_ROOT }}"
            - name: SENDGRID_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: sendgridApiKey
                  optional: false
            - name: CM_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: coinmetricsApiKey
                  optional: false
            - name: CM_API_BASE_URL
              value: "{{ .Values.dataPusher.coinmetricsApiBaseUrl }}"
            - name: CM_DATA_PUSHER_REDIS_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: redisUsername
                  optional: false
            - name: CM_DATA_PUSHER_REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: redisPassword
                  optional: false
            - name: CM_DATA_PUSHER_CELERY_BROKER_URL
              value: redis://$(CM_DATA_PUSHER_REDIS_USERNAME):$(CM_DATA_PUSHER_REDIS_PASSWORD)@{{ include "data-pusher.fullname" . }}-{{ .Values.redis.host }}:{{ .Values.redis.port }}
            - name: CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: dataPusherFieldEncryptionKey
                  optional: false
            - name: CM_DATA_PUSHER_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: dataPusherCookieSecretKey
                  optional: false
            - name: CM_SFTP_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: coinmetricsSFTPKey
                  optional: false
            - name: CM_SFTP_RSA_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: coinmetricsSftpRsaKey
                  optional: false
            - name: CM_DATA_PUSHER_SECRET_KEY_OVERRIDE
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: dataPusherCookieSecretKeyOverride
                  optional: false
            - name: CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY_OVERRIDE
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: dataPusherCookieFieldEncryptionKeyOverride
                  optional: false
            - name: MINIO_URL
              value: "{{ .Values.dataPusher.minioUrl }}"
            - name: MINIO_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: minioAccessKeyID
                  optional: false
            - name: MINIO_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "data-pusher.fullname" . }}
                  key: minioSecretAccessKey
                  optional: false
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          name: data-pusher
          ports:
            - containerPort: 8000
              name: http
              protocol: TCP
          {{- with .Values.dataPusher.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: state
              mountPath: /opt/coinmetrics/data-pusher
      terminationGracePeriodSeconds: 10
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: state
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: {{ .Values.dataPusher.persistence.size }}
        storageClassName: {{ .Values.dataPusher.persistence.storageClassName }}
        volumeMode: Filesystem

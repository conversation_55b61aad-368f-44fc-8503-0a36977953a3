---
dataPusher:
  coinmetricsApiBaseUrl: http://apisix-gateway-unencrypted.apisix-gateway.svc.cp1.jfk1.local/v4
  oauthBaseRedirectUri: https://data-pusher-cp1.cnmtrcs.io
  replicaCount: "1"
  environment: "PRODUCTION"
  location: "CP1"
  sendgridEmail: <EMAIL>
  CM_SFTP_SERVER: sftp.coinmetrics.io
  CM_SFTP_USERNAME: data-pusher.flat.files
  CM_SFTP_PORT: "2222"
  CM_SFTP_DATA_ROOT: /opt/sftp-server-data/
  minioUrl: minio.cnmtrcs.io:9002
  fidelityIndexUrl: http://md-factory-fidelity-index.md-factory.svc:8080
  pgPort: "5432"
  pgDatabaseIndices: pg-indices-1
  pgDatabaseTrades: pg-trades-spot-1-r-1
  pgHost: pgbouncer.pgbouncer
  pgUser: postgres
  persistence:
    storageClassName: zfs-wffc
    size: 10Gi
  tolerations:
    - key: "coinmetrics.io/zfs-only"
      operator: "Exists"
      effect: "NoExecute"
  nodeAffinity: []

redis:
  replicaCount: "1"
  persistence:
    storageClassName: zfs-wffc
    size: 128Mi
  tolerations:
    - key: "coinmetrics.io/zfs-only"
      operator: "Exists"
      effect: "NoExecute"
  nodeAffinity: []

virtualServer:
  domain: data-pusher-cp1.cnmtrcs.io
  ingressClassName: nginx-internal

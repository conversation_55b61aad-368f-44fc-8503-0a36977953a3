---
dataPusher:
  # Also, the internal Kubernetes URL can be used
  # http://apisix-gateway-unencrypted.apisix-gateway.svc.cdev1.dfw1.local/v4
  coinmetricsApiBaseUrl: https://api.coinmetrics.io/v4
  oauthBaseRedirectUri: https://data-pusher-stg.cnmtrcs.io
  replicaCount: "1"
  replicaCountRedis: "1"
  environment: "STAGING"
  location: "CDEV1"
  sendgridEmail: <EMAIL>
  CM_SFTP_SERVER: sftp.coinmetrics.io
  CM_SFTP_USERNAME: data-pusher.flat.files
  CM_SFTP_PORT: "2222"
  CM_SFTP_DATA_ROOT: /opt/sftp-server-data/
  minioUrl: minio.cnmtrcs.io:9002
  fidelityIndexUrl: http://md-factory-fidelity-index.md-factory.svc:8080
  pgDatabaseTrades: pg-trades-spot-stg-cdev1-p
  pgDatabaseIndices: pg-indices-1
  pgPort: "5432"
  pgHost: pgbouncer.pgbouncer
  pgUser: postgres
  persistence:
    storageClassName: zfs
    size: 512Mi
  tolerations:
    - key: "coinmetrics.io/zfs-only"
      operator: "Exists"
      effect: "NoExecute"
  nodeAffinity: []

redis:
  replicaCount: "1"
  persistence:
    storageClassName: zfs
    size: 128Mi
  tolerations:
    - key: "coinmetrics.io/zfs-only"
      operator: "Exists"
      effect: "NoExecute"
  nodeAffinity: []

virtualServer:
  domain: data-pusher-stg.cnmtrcs.io
  ingressClassName: nginx-internal

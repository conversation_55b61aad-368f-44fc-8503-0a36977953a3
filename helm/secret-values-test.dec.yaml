ENVIRONMENT: LOCAL
CM_SFTP_SERVER: sftp.coinmetrics.io
CM_SFTP_USERNAME: data-pusher.flat.files
CM_SFTP_PORT: "2222"
CM_SFTP_DATA_ROOT: /opt/sftp-server-data/
SENDGRID_API_KEY: *********************************************************************
SENDGRID_EMAIL: <EMAIL>
PG_TRADES_DB: test_db
PG_SCHEMA: staging
PG_INDEX_DB: test_db
CM_API_KEY: 2xEyXpIBsjyATfAuGk3u
CM_SFTP_PRIVATE_KEY: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
AWS_ACCESS_KEY_ID: cm-data-pusher
AWS_SECRET_ACCESS_KEY: YjhhYThiMTI5MTI2MDNjMDFhMGNkYjY4
S3_BACKUP_BUCKET: data-pusher-sqlite-backups
S3_TARGET_URL: https://minio.cnmtrcs.io:9002
DATA_PUSHER_DB: /tmp/cm-data-pusher/db.sqlite
MINIO_ACCESS_KEY_ID: cm-data-pusher
MINIO_BUCKET: data-pusher-sqlite-backups
MINIO_SECRET_ACCESS_KEY: YjhhYThiMTI5MTI2MDNjMDFhMGNkYjY4
MINIO_URL: https://minio.cnmtrcs.io:9002
CM_DATA_PUSHER_DB: /tmp/cm-data-pusher/db.sqlite
GOOGLE_CLIENT_ID: 962118171407-6m5gpqnjjboghtc6dvkmufa0nbuqkicb.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET: 9UJJThPw5B_ZYvo_SQ8aJ7K0
GOOGLE_REFRESH_TOKEN: 1//04iC7fZ0KeolfCgYIARAAGAQSNwF-L9Ir6QtOoR1hkiXzGXewewKs0pu-AGifx79fCx9xI8eYQSCs5WJMiT28nme_sDcgISKtBGg
CM_DATA_PUSHER_SECRET_KEY: gPZmZvSJ9EeqFFSJ7nMiPK6wkHDsGs8b2i5aFE3oDXk=
CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY: mHJJRHOerSQxzKNIVmPo1elGYGymRYKXwO7+YmMYSl4=

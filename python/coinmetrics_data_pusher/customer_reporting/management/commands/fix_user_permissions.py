from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.db import transaction


class Command(BaseCommand):
    help = 'Fix admin permissions for existing OAuth users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Fix permissions for a specific user email',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making actual changes',
        )
        parser.add_argument(
            '--domain',
            type=str,
            help='Fix permissions for all users with a specific domain (e.g., coinmetrics-old.io)',
        )

    def handle(self, *args, **options):
        User = get_user_model()
        dry_run = options['dry_run']
        email = options['email']
        domain = options['domain']

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        # Determine which users to fix
        if email:
            users = User.objects.filter(email=email)
            if not users.exists():
                self.stdout.write(self.style.ERROR(f'No user found with email: {email}'))
                return
        elif domain:
            users = User.objects.filter(email__endswith=f'@{domain}')
            if not users.exists():
                self.stdout.write(self.style.ERROR(f'No users found with domain: @{domain}'))
                return
        else:
            # Fix all users who lack admin permissions
            users = User.objects.filter(is_staff=False) | User.objects.filter(is_superuser=False)
            if not users.exists():
                self.stdout.write(self.style.SUCCESS('All users already have admin permissions'))
                return

        self.stdout.write(f'Found {users.count()} users to check/fix')

        for user in users:
            needs_fix = not user.is_staff or not user.is_superuser
            
            if needs_fix:
                self.stdout.write(f'User {user.email}: is_staff={user.is_staff}, is_superuser={user.is_superuser}')
                
                if dry_run:
                    self.stdout.write(f'WOULD FIX: {user.email} - grant admin permissions')
                else:
                    try:
                        with transaction.atomic():
                            # Grant all permissions
                            all_permissions = Permission.objects.all()
                            for permission in all_permissions:
                                user.user_permissions.add(permission)
                            
                            # Set admin flags
                            user.is_staff = True
                            user.is_superuser = True
                            
                            # Set username if not set
                            if not user.username:
                                user.username = user.email.split("@")[0]
                            
                            user.save()
                            
                            self.stdout.write(
                                self.style.SUCCESS(f'FIXED: {user.email} - granted admin permissions')
                            )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'ERROR fixing {user.email}: {str(e)}')
                        )
            else:
                self.stdout.write(f'OK: {user.email} already has admin permissions')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('Dry run completed. Run without --dry-run to make actual changes.')
            )
        else:
            self.stdout.write(self.style.SUCCESS('Permission fix completed!'))

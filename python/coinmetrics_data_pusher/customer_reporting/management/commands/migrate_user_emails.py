from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction


class Command(BaseCommand):
    help = 'Migrate user emails from coinmetrics.io to coinmetrics-old.io domain'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making actual changes',
        )
        parser.add_argument(
            '--from-domain',
            type=str,
            default='coinmetrics.io',
            help='Source domain to migrate from (default: coinmetrics.io)',
        )
        parser.add_argument(
            '--to-domain',
            type=str,
            default='coinmetrics-old.io',
            help='Target domain to migrate to (default: coinmetrics-old.io)',
        )

    def handle(self, *args, **options):
        User = get_user_model()
        dry_run = options['dry_run']
        from_domain = options['from_domain']
        to_domain = options['to_domain']

        # Find all users with the old domain
        old_domain_users = User.objects.filter(email__endswith=f'@{from_domain}')
        
        if not old_domain_users.exists():
            self.stdout.write(
                self.style.WARNING(f'No users found with @{from_domain} domain')
            )
            return

        self.stdout.write(
            self.style.SUCCESS(f'Found {old_domain_users.count()} users with @{from_domain} domain')
        )

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
            
        for user in old_domain_users:
            old_email = user.email
            new_email = old_email.replace(f'@{from_domain}', f'@{to_domain}')
            
            # Check if a user with the new email already exists
            if User.objects.filter(email=new_email).exists():
                self.stdout.write(
                    self.style.ERROR(
                        f'SKIPPING: User with email {new_email} already exists. '
                        f'Cannot migrate {old_email}'
                    )
                )
                continue
            
            if dry_run:
                self.stdout.write(f'WOULD CHANGE: {old_email} -> {new_email}')
            else:
                try:
                    with transaction.atomic():
                        user.email = new_email
                        user.save()
                        self.stdout.write(
                            self.style.SUCCESS(f'MIGRATED: {old_email} -> {new_email}')
                        )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'ERROR migrating {old_email}: {str(e)}')
                    )

        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    'Dry run completed. Run without --dry-run to make actual changes.'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('Email migration completed successfully!')
            )

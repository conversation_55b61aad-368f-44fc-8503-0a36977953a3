from django.core.management.base import BaseCommand
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory


class Command(BaseCommand):
    help = 'Test OAuth logging configuration'

    def handle(self, *args, **options):
        logger = LoggerFactory.get_logger("oauth_views")
        
        self.stdout.write("Testing OAuth logging configuration...")
        
        # Test different log levels
        logger.info("INFO: OAuth logging test - this should appear in logs")
        logger.warning("WARNING: OAuth logging test - this should appear in logs")
        logger.error("ERROR: OAuth logging test - this should appear in logs")
        
        self.stdout.write(
            self.style.SUCCESS('OAuth logging test completed. Check your logs for the test messages.')
        )
        
        self.stdout.write("Log messages should appear with format:")
        self.stdout.write("[timestamp] [process] [level] [oauth_views] - message")

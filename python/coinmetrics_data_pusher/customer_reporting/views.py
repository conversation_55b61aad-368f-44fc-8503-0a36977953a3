import os
import requests
from django.shortcuts import redirect, render
from django.contrib.auth import login, authenticate, get_user_model
from urllib.parse import urlencode
from django.contrib.auth.models import Permission
from .coinmetrics_api_scripts.util.logger_factory import LoggerFactory

# Set up logger for OAuth views using the application's logger factory
logger = LoggerFactory.get_logger("oauth_views")


REDIRECT_URI = "/accounts/oauth/callback/"
def admin_login(request):
    logger.info("Admin login page accessed")
    if request.user.is_authenticated:
        logger.info(f"User {request.user.email} already authenticated, redirecting to admin")
        return redirect('/admin/')
    logger.info("User not authenticated, showing login page")
    return render(request, 'admin_login.html')


def start_auth(request):
    logger.info("OAuth authentication started")

    try:
        client_id = os.environ.get("OAUTH_CLIENT_ID")
        base_redirect_uri = os.environ.get('OAUTH_BASE_REDIRECT_URI')

        if not client_id:
            logger.error("OAUTH_CLIENT_ID environment variable not set")
            raise ValueError("OAuth client ID not configured")

        if not base_redirect_uri:
            logger.error("OAUTH_BASE_REDIRECT_URI environment variable not set")
            raise ValueError("OAuth base redirect URI not configured")

        logger.info(f"Using client_id: {client_id[:10]}... (truncated)")
        logger.info(f"Using redirect_uri: {base_redirect_uri}{REDIRECT_URI}")

        authorize_url = "https://accounts.google.com/o/oauth2/auth"
        params = {
            "client_id": client_id,
            "response_type": "code",
            "redirect_uri": f"{base_redirect_uri}{REDIRECT_URI}",
            "scope": "email",
        }
        url = f'{authorize_url}?{urlencode(params)}'

        logger.info(f"Redirecting to Google OAuth: {authorize_url}")
        return redirect(url)

    except Exception as e:
        logger.error(f"Error in start_auth: {str(e)}", exc_info=True)
        raise


def oauth_callback(request):
    logger.info("OAuth callback received")

    try:
        User = get_user_model()
        token_url = "https://accounts.google.com/o/oauth2/token"
        code = request.GET.get('code')

        if not code:
            logger.error("No authorization code received in callback")
            raise ValueError("Authorization code missing from callback")

        logger.info(f"Authorization code received: {code[:10]}... (truncated)")

        # Validate environment variables
        client_id = os.environ.get("OAUTH_CLIENT_ID")
        client_secret = os.environ.get("OAUTH_CLIENT_SECRET")
        base_redirect_uri = os.environ.get('OAUTH_BASE_REDIRECT_URI')

        if not client_id:
            logger.error("OAUTH_CLIENT_ID environment variable not set")
            raise ValueError("OAuth client ID not configured")
        if not client_secret:
            logger.error("OAUTH_CLIENT_SECRET environment variable not set")
            raise ValueError("OAuth client secret not configured")
        if not base_redirect_uri:
            logger.error("OAUTH_BASE_REDIRECT_URI environment variable not set")
            raise ValueError("OAuth base redirect URI not configured")

        data = {
            "code": code,
            "client_id": client_id,
            "client_secret": client_secret,
            "redirect_uri": f"{base_redirect_uri}{REDIRECT_URI}",
            "scope": "email",
            "grant_type": "authorization_code",
        }

        logger.info("Requesting access token from Google")
        response = requests.post(token_url, data=data)

        if response.status_code != 200:
            logger.error(f"Token request failed with status {response.status_code}: {response.text}")
            raise ValueError(f"Token request failed: {response.status_code}")

        token_data = response.json()
        logger.info(f"Token response received: {list(token_data.keys())}")

        access_token = token_data.get('access_token')
        if not access_token:
            logger.error(f"No access token in response: {token_data}")
            raise ValueError("Access token not received from Google")

        logger.info("Requesting user info from Google")
        userinfo_url = "https://www.googleapis.com/oauth2/v3/userinfo"
        headers = {'Authorization': f'Bearer {access_token}'}
        userinfo_response = requests.get(userinfo_url, headers=headers)

        if userinfo_response.status_code != 200:
            logger.error(f"User info request failed with status {userinfo_response.status_code}: {userinfo_response.text}")
            raise ValueError(f"User info request failed: {userinfo_response.status_code}")

        userinfo_data = userinfo_response.json()
        logger.info(f"User info received: {list(userinfo_data.keys())}")

        email = userinfo_data.get('email')
        if not email:
            logger.error(f"No email in user info response: {userinfo_data}")
            raise ValueError("Email not received from Google")

        logger.info(f"User email from Google: {email}")

        # Handle domain migration from coinmetrics.io to coinmetrics-old.io
        logger.info("Starting user lookup/creation process")
        user = None
        created = False

        # First try to find user with current email
        logger.info(f"Looking for existing user with email: {email}")
        try:
            user = User.objects.get(email=email)
            logger.info(f"Found existing user with current email: {email}")
            created = False
        except User.DoesNotExist:
            logger.info(f"No user found with current email: {email}")

            # If not found and email is coinmetrics-old.io, check for old coinmetrics.io version
            if email.endswith('@coinmetrics-old.io'):
                old_email = email.replace('@coinmetrics-old.io', '@coinmetrics.io')
                logger.info(f"Checking for user with old domain email: {old_email}")

                try:
                    user = User.objects.get(email=old_email)
                    logger.info(f"Found user with old domain email: {old_email}, migrating to new domain")

                    # Update the user's email to the new domain
                    user.email = email
                    user.save()
                    logger.info(f"Successfully migrated user email from {old_email} to {email}")
                    created = False

                except User.DoesNotExist:
                    logger.info(f"No user found with old domain email: {old_email}, creating new user")
                    # Create new user with current email
                    user = User.objects.create(email=email)
                    logger.info(f"Created new user with email: {email}")
                    created = True
            else:
                logger.info(f"Email doesn't end with @coinmetrics-old.io, creating new user")
                # Create new user with current email
                user = User.objects.create(email=email)
                logger.info(f"Created new user with email: {email}")
                created = True
        # Set up user for authentication
        logger.info(f"Setting up user for authentication: {user.email}")
        user.backend = 'django.contrib.auth.backends.ModelBackend'

        if created:
            logger.info("User was newly created, setting up permissions and attributes")
            try:
                all_permissions = Permission.objects.all()
                logger.info(f"Adding {all_permissions.count()} permissions to new user")

                for permission in all_permissions:
                    user.user_permissions.add(permission)

                user.is_staff = True
                user.username = email.split("@")[0]
                user.is_superuser = True
                user.save()
                logger.info(f"Successfully set up new user: {user.email} with username: {user.username}")

            except Exception as e:
                logger.error(f"Error setting up new user permissions: {str(e)}", exc_info=True)
                raise
        else:
            logger.info("Using existing user, checking admin permissions")
            # Check if existing user has admin permissions, if not, grant them
            if not user.is_staff or not user.is_superuser:
                logger.info(f"Existing user {user.email} lacks admin permissions, granting them")
                try:
                    all_permissions = Permission.objects.all()
                    logger.info(f"Adding {all_permissions.count()} permissions to existing user")

                    for permission in all_permissions:
                        user.user_permissions.add(permission)

                    user.is_staff = True
                    user.is_superuser = True
                    if not user.username:
                        user.username = email.split("@")[0]
                    user.save()
                    logger.info(f"Successfully granted admin permissions to existing user: {user.email}")

                except Exception as e:
                    logger.error(f"Error granting admin permissions to existing user: {str(e)}", exc_info=True)
                    raise
            else:
                logger.info(f"Existing user {user.email} already has admin permissions (is_staff={user.is_staff}, is_superuser={user.is_superuser})")

        # Final save and login
        user.save()
        logger.info(f"Logging in user: {user.email}")
        login(request, user)
        logger.info(f"Successfully logged in user: {user.email}, redirecting to admin")
        return redirect('/admin')

    except Exception as e:
        logger.error(f"OAuth callback failed: {str(e)}", exc_info=True)
        # You might want to redirect to an error page or show a user-friendly message
        raise

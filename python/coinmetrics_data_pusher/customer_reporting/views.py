import os
import requests
from django.shortcuts import redirect, render
from django.contrib.auth import login, authenticate, get_user_model
from urllib.parse import urlencode
from django.contrib.auth.models import Permission


REDIRECT_URI = "/accounts/oauth/callback/"
def admin_login(request):
    if request.user.is_authenticated:
        return redirect('/admin/')
    return render(request, 'admin_login.html')


def start_auth(request):
    authorize_url = "https://accounts.google.com/o/oauth2/auth"
    params = {
        "client_id": os.environ.get("OAUTH_CLIENT_ID"),
        "response_type": "code",
        "redirect_uri": f"{os.environ.get('OAUTH_BASE_REDIRECT_URI')}{REDIRECT_URI}",
        "scope": "email",
    }
    url = f'{authorize_url}?{urlencode(params)}'
    return redirect(url)


def oauth_callback(request):
    User = get_user_model()
    token_url = "https://accounts.google.com/o/oauth2/token"
    code = request.GET.get('code')
    data = {
        "code": code,
        "client_id": os.environ.get("OAUTH_CLIENT_ID"),
        "client_secret": os.environ.get("OAUTH_CLIENT_SECRET"),
        "redirect_uri": f"{os.environ.get('OAUTH_BASE_REDIRECT_URI')}{REDIRECT_URI}",
        "scope": "email",
        "grant_type": "authorization_code",
    }
    response = requests.post(token_url, data=data)
    token_data = response.json()
    access_token = token_data.get('access_token')

    userinfo_url = "https://www.googleapis.com/oauth2/v3/userinfo"
    headers = {'Authorization': f'Bearer {access_token}'}
    userinfo_response = requests.get(userinfo_url, headers=headers)
    userinfo_data = userinfo_response.json()

    email = userinfo_data.get('email')

    # Handle domain migration from coinmetrics.io to coinmetrics-old.io
    user = None
    created = False

    # First try to find user with current email
    try:
        user = User.objects.get(email=email)
        created = False
    except User.DoesNotExist:
        # If not found and email is coinmetrics-old.io, check for old coinmetrics.io version
        if email.endswith('@coinmetrics-old.io'):
            old_email = email.replace('@coinmetrics-old.io', '@coinmetrics.io')
            try:
                user = User.objects.get(email=old_email)
                # Update the user's email to the new domain
                user.email = email
                user.save()
                created = False
            except User.DoesNotExist:
                # Create new user with current email
                user = User.objects.create(email=email)
                created = True
        else:
            # Create new user with current email
            user = User.objects.create(email=email)
            created = True
    user.backend = 'django.contrib.auth.backends.ModelBackend'
    if created:
        all_permissions = Permission.objects.all()
        for permission in all_permissions:
            user.user_permissions.add(permission)
        user.is_staff = True
        user.username = email.split("@")[0]
        user.is_superuser = True
        user.save()
    user.save()
    login(request, user)
    return redirect('/admin')

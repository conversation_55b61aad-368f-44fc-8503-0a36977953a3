import os
from urllib.parse import urlencode

import requests
from django.contrib.auth import login, get_user_model
from django.contrib.auth.models import Permission
from django.shortcuts import redirect, render

from .coinmetrics_api_scripts.util.logger_factory import LoggerFactory

# Set up logger for OAuth views using the application's logger factory
logger = LoggerFactory.get_logger("oauth_views")


REDIRECT_URI = "/accounts/oauth/callback/"
def admin_login(request):
    logger.debug("Admin login page accessed")
    if request.user.is_authenticated:
        logger.debug(f"User {request.user.email} already authenticated, redirecting to admin")
        return redirect('/admin/')
    logger.debug("User not authenticated, showing login page")
    return render(request, 'admin_login.html')


def start_auth(request):
    logger.debug("OAuth authentication started")

    try:
        client_id = os.environ.get("OAUTH_CLIENT_ID")
        base_redirect_uri = os.environ.get('OAUTH_BASE_REDIRECT_URI')

        if not client_id:
            logger.error("OAUTH_CLIENT_ID environment variable not set")
            raise ValueError("OAuth client ID not configured")

        if not base_redirect_uri:
            logger.error("OAUTH_BASE_REDIRECT_URI environment variable not set")
            raise ValueError("OAuth base redirect URI not configured")

        logger.debug(f"Using client_id: {client_id[:5]}... (truncated)")
        logger.debug(f"Using redirect_uri: {base_redirect_uri}{REDIRECT_URI}")

        authorize_url = "https://accounts.google.com/o/oauth2/auth"
        params = {
            "client_id": client_id,
            "response_type": "code",
            "redirect_uri": f"{base_redirect_uri}{REDIRECT_URI}",
            "scope": "email",
        }
        url = f'{authorize_url}?{urlencode(params)}'

        logger.debug(f"Redirecting to Google OAuth: {authorize_url}")
        return redirect(url)

    except Exception as e:
        logger.error(f"Error in start_auth: {str(e)}", exc_info=True)
        raise


def oauth_callback(request):
    logger.debug("OAuth callback received")

    try:
        User = get_user_model()
        token_url = "https://accounts.google.com/o/oauth2/token"
        code = request.GET.get('code')

        if not code:
            logger.error("No authorization code received in callback")
            raise ValueError("Authorization code missing from callback")

        logger.debug(f"Authorization code received: {code[:5]}... (truncated)")

        # Validate environment variables
        client_id = os.environ.get("OAUTH_CLIENT_ID")
        client_secret = os.environ.get("OAUTH_CLIENT_SECRET")
        base_redirect_uri = os.environ.get('OAUTH_BASE_REDIRECT_URI')

        if not client_id:
            logger.error("OAUTH_CLIENT_ID environment variable not set")
            raise ValueError("OAuth client ID not configured")
        if not client_secret:
            logger.error("OAUTH_CLIENT_SECRET environment variable not set")
            raise ValueError("OAuth client secret not configured")
        if not base_redirect_uri:
            logger.error("OAUTH_BASE_REDIRECT_URI environment variable not set")
            raise ValueError("OAuth base redirect URI not configured")

        data = {
            "code": code,
            "client_id": client_id,
            "client_secret": client_secret,
            "redirect_uri": f"{base_redirect_uri}{REDIRECT_URI}",
            "scope": "email",
            "grant_type": "authorization_code",
        }

        logger.debug("Requesting access token from Google")
        response = requests.post(token_url, data=data)

        if response.status_code != 200:
            logger.error(f"Token request failed with status {response.status_code}: {response.text}")
            raise ValueError(f"Token request failed: {response.status_code}")

        token_data = response.json()
        logger.debug(f"Token response received: {list(token_data.keys())}")

        access_token = token_data.get('access_token')
        if not access_token:
            logger.error(f"No access token in response: {token_data}")
            raise ValueError("Access token not received from Google")

        logger.debug("Requesting user info from Google")
        userinfo_url = "https://www.googleapis.com/oauth2/v3/userinfo"
        headers = {'Authorization': f'Bearer {access_token}'}
        userinfo_response = requests.get(userinfo_url, headers=headers)

        if userinfo_response.status_code != 200:
            logger.error(f"User info request failed with status {userinfo_response.status_code}: {userinfo_response.text}")
            raise ValueError(f"User info request failed: {userinfo_response.status_code}")

        userinfo_data = userinfo_response.json()
        logger.debug(f"User info received: {list(userinfo_data.keys())}")

        email = userinfo_data.get('email')
        if not email:
            logger.error(f"No email in user info response: {userinfo_data}")
            raise ValueError("Email not received from Google")

        logger.debug(f"User email from Google: {email}")

        # Get or create user (handle potential duplicates)
        logger.debug(f"Looking for existing active user or creating new one with email: {email}")

        # First, let's see what users exist for debugging
        all_users_with_email = User.objects.filter(email=email)
        logger.info(f"Found {all_users_with_email.count()} total users with email {email}")
        for i, u in enumerate(all_users_with_email):
            logger.info(f"  User {i+1}: ID={u.id}, username='{u.username}', is_active={u.is_active}, "
                       f"is_staff={u.is_staff}, date_joined={u.date_joined}, entire user: {u}")

        try:
            user, created = User.objects.get_or_create(
                email=email,
                is_active=True,
                defaults={
                    'username': email,
                    'is_active': True,
                }
            )
            logger.info(f"get_or_create result: user.id={user.id}, created={created}")
        except User.MultipleObjectsReturned:
            logger.warning(f"Multiple active users found with email {email}")
            # Show all active users for debugging
            active_users = User.objects.filter(email=email, is_active=True)
            logger.warning(f"Found {active_users.count()} active users:")
            for i, u in enumerate(active_users):
                logger.warning(f"  Active User {i+1}: ID={u.id}, username='{u.username}', "
                              f"date_joined={u.date_joined}, entire user: {u}")

            # Use the most recent active one
            user = active_users.order_by('-date_joined').first()
            created = False
            logger.warning(f"Using most recent user: ID={user.id}, date_joined={user.date_joined}")
        user.backend = 'django.contrib.auth.backends.ModelBackend'

        if created:
            logger.debug("User was newly created, setting up permissions and attributes")
            try:
                all_permissions = Permission.objects.all()
                logger.debug(f"Adding {all_permissions.count()} permissions to new user")

                for permission in all_permissions:
                    user.user_permissions.add(permission)

                user.is_staff = True
                user.is_superuser = True
                user.save()
                logger.debug(f"Successfully set up new user: {user.email} with username: {user.username}")

            except Exception as e:
                logger.error(f"Error setting up new user permissions: {str(e)}", exc_info=True)
                raise
        else:
            logger.debug(f"Found existing active user: {user.email}")

        # Login user
        logger.debug(f"Logging in user: {user.email}")
        login(request, user)
        logger.debug(f"Successfully logged in user: {user.email}, redirecting to admin")
        return redirect('/admin')

    except Exception as e:
        logger.error(f"OAuth callback failed: {str(e)}", exc_info=True)
        # You might want to redirect to an error page or show a user-friendly message
        raise

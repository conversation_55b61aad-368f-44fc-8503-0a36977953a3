---
image: docker.io/coinmetrics/nix-builder:latest

stages:
  - build
  - test
  - publish
  - staging
  - production

build:
  variables:
    KUBERNETES_CPU_REQUEST: "8"
    KUBERNETES_CPU_LIMIT: "8"
    KUBERNETES_MEMORY_REQUEST: "16Gi"
    KUBERNETES_MEMORY_LIMIT: "16Gi"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  stage: build
  before_script:
    - source /before-script.sh
  script:
    - nix --print-build-logs build .#cm-data-pusher-image
  after_script:
    - /after-script.sh
  tags:
    - rt-containerd
  timeout: "1 day"

test:
  variables:
    KUBERNETES_CPU_REQUEST: "8"
    KUBERNETES_CPU_LIMIT: "8"
    KUBERNETES_MEMORY_REQUEST: "16Gi"
    KUBERNETES_MEMORY_LIMIT: "16Gi"

    POSTGRES_DB: $POSTGRES_DB
    POSTGRES_USER: $POSTGRES_USER
    POSTGRES_PASSWORD: $POSTGRES_PASSWORD
    POSTGRES_HOST_AUTH_METHOD: trust
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  stage: test
  services:
    - postgres
  before_script:
    - source /before-script.sh
  script:
    - nix run .#integrationTests
  after_script:
    - /after-script.sh
  tags:
    - rt-containerd

publish:
  stage: publish
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
  before_script:
    - source /before-script.sh
  script:
    - nix --print-build-logs run .#login
    - nix --print-build-logs run .#publish
  after_script:
    - /after-script.sh
  tags:
    - rt-containerd

deploy staging:
  stage: staging
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
  before_script:
    - source /before-script.sh
  script:
    - nix --print-build-logs run .#deploy -- cdev1
  after_script:
    - /after-script.sh
  tags:
    - env-cdev1

deploy cp1:
  stage: production
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
  before_script:
    - source /before-script.sh
  script:
    - nix --print-build-logs run .#deploy -- cp1
  after_script:
    - /after-script.sh
  tags:
    - rt-containerd
    - env-cp1

deploy celery-exporter staging:
  image: registry.gitlab.com/coinmetrics/ops/cicd-tools:0.2.0-stable
  stage: staging
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
  dependencies: [ ]
  script:
    - cd ./helm && helm upgrade data-pusher-celery-exporter celery-exporter --install -n data-pusher --debug --wait -f celery-exporter-values-cdev1.yaml -f secrets://celery-exporter-secret-values-cdev1.yaml
  tags:
    - env-cdev1

deploy celery-exporter cp1:
  image: registry.gitlab.com/coinmetrics/ops/cicd-tools:0.2.0-stable
  stage: production
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
  dependencies: [ ]
  script:
    - cd ./helm && helm upgrade data-pusher-celery-exporter celery-exporter --install -n data-pusher --debug --wait -f celery-exporter-values-cp1.yaml -f secrets://celery-exporter-secret-values-cp1.yaml
  tags:
    - kube-cp1-small
    - linux
    - cp1
